# FixGuru

一个使用DynamoDB作为数据库的企业级平台，支持本地开发和AWS Lambda部署。

## 📋 项目概述

### 项目特点
- **Spring Boot 3.2.5** + **JDK 17** 现代化技术栈
- **DynamoDB** NoSQL数据库，支持本地开发和AWS云端
- **AWS Lambda** 无服务器架构支持
- **Docker Compose** 一键启动本地开发环境
- **Knife4j** API文档自动生成
- **完整的用户管理功能** 演示CRUD操作

### 技术架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   API Gateway   │    │   AWS Lambda    │
│   (iOS/Web)     │───▶│                 │───▶│   Spring Boot   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐             │
                       │     Redis       │◀────────────┤
                       │   (缓存服务)     │             │
                       └─────────────────┘             │
                                                        │
                       ┌─────────────────┐             │
                       │   DynamoDB      │◀────────────┘
                       │  (主数据库)      │
                       └─────────────────┘
```

## 🚀 快速开始

### 环境要求
- **JDK 17+**
- **Maven 3.9.6+**
- **Docker & Docker Compose**
- **Git**

### 1. 克隆项目
```bash
git clone https://github.com/your-company/FixGuru-Platform.git
cd FixGuru-Platform
```

### 本地开发环境
```bash
git clone https://github.com/your-company/FixGuru-Platform.git
cd FixGuru-Platform
./scripts/local/start.sh
```

### 3. 运行应用
```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run
```

### 4. 验证服务
- **应用地址**: http://localhost:8080/api
- **API文档**: http://localhost:8080/api/doc.html
- **健康检查**: http://localhost:8080/api/v1/health
- **DynamoDB Admin**: http://localhost:8001 (可选)

## 📚 API接口说明

### 健康检查接口
```bash
# 基础健康检查
curl http://localhost:8080/api/v1/health

# 详细健康检查
curl http://localhost:8080/api/v1/health/detailed
```

### 用户管理接口

#### 1. 创建用户
```bash
curl -X POST http://localhost:8080/api/v1/users \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "Test123456",
    "confirmPassword": "Test123456"
  }'
```

#### 2. 用户登录
```bash
curl -X POST http://localhost:8080/api/v1/users/login \
  -H "Content-Type: application/json" \
  -d '{
    "account": "testuser",
    "password": "Test123456"
  }'
```

#### 3. 获取用户信息
```bash
# 根据用户ID获取
curl http://localhost:8080/api/v1/users/{userId}

# 根据用户名获取
curl http://localhost:8080/api/v1/users/username/{username}

# 根据邮箱获取
curl http://localhost:8080/api/v1/users/email/{email}
```

#### 4. 检查用户名/邮箱是否存在
```bash
# 检查用户名
curl http://localhost:8080/api/v1/users/check/username/{username}

# 检查邮箱
curl http://localhost:8080/api/v1/users/check/email/{email}
```

## 🔧 本地开发配置

### DynamoDB Local
- **端口**: 8000
- **管理界面**: http://localhost:8001
- **数据存储**: `./docker/dynamodb-local/data`
- **表自动创建**: 应用启动时自动创建User表

### 应用配置
- **本地配置**: `src/main/resources/application-local.yml`
- **AWS配置**: `src/main/resources/application-aws.yml`

### 开发工具
- **API文档**: Knife4j提供交互式API文档
- **日志**: 本地环境开启DEBUG级别日志
- **热重载**: 支持代码修改后自动重启

## ☁️ AWS部署

### 前置要求

1. [AWS CLI](https://aws.amazon.com/cli/) 已安装并配置
2. [Docker](https://www.docker.com/) 已安装
3. [Maven 3.9.6+](https://maven.apache.org/)

### 部署步骤

#### 1. 配置AWS凭证

```bash
aws configure
```

#### 2. 部署DynamoDB表

```bash
# 部署DynamoDB基础设施
./scripts/aws/deploy-dynamodb.sh

# 或使用AWS CLI直接部署
aws cloudformation deploy \
  --template-file aws/cloudformation/dynamodb-table.yaml \
  --stack-name fixguru-dynamodb-table \
  --parameter-overrides \
      ProjectName=fixguru-platform \
      Environment=dev \
  --capabilities CAPABILITY_IAM
```

#### 3. 部署应用

```bash
# 使用SAM部署完整应用
./scripts/deploy.sh dev

# 或使用CI/CD自动化部署
./scripts/deploy-pipeline.sh dev
```

#### 4. 验证部署

```bash
# 检查应用健康状态
curl -X GET "${API_GATEWAY_URL}/api/v1/health"

# 访问API文档
open "${API_GATEWAY_URL}/doc.html"
```

## 🧪 测试和验证

### 本地测试
```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn verify
```

### API测试

1. 启动应用后访问: http://localhost:8080/api/doc.html
2. 使用Postman导入: postman/FixGuru-Platform.postman_collection.json
3. 参考API文档进行测试

## 📁 项目结构
```
FixGuru-Platform/
├── src/
│   ├── main/
│   │   ├── java/com/fixguru/
│   │   │   ├── controller/          # REST控制器
│   │   │   ├── service/             # 业务服务层
│   │   │   ├── domain/              # 数据实体
│   │   │   ├── dto/                 # 数据传输对象
│   │   │   ├── config/              # 配置类
│   │   │   ├── aws/                 # AWS服务集成
│   │   │   ├── common/              # 通用组件
│   │   │   ├── constants/           # 常量定义
│   │   │   ├── exception/           # 异常处理
│   │   │   ├── utils/               # 工具类
│   │   │   └── enums/               # 枚举定义
│   │   └── resources/               # 配置文件
│   └── assembly/                    # 构建配置
├── docker/                          # Docker配置
├── scripts/                         # 脚本目录
├── target/                          # 构建输出
├── pom.xml                         # Maven配置
├── docker-compose.yml              # Docker Compose配置
└── README.md                       # 项目说明
```

## 🔍 常见问题

### Q: DynamoDB Local启动失败？
A: 检查端口8000是否被占用，确保Docker服务正常运行。

### Q: 无法连接到DynamoDB？
A: 确认配置文件中的endpoint和凭证设置正确。

### Q: Lambda部署后无法访问？
A: 检查IAM角色权限，确保Lambda有访问DynamoDB的权限。

### Q: API文档无法访问？
A: 确认Knife4j配置正确，检查应用是否正常启动。

## 👥 联系我们

- **团队**: FixGuru Team
- **邮箱**: <EMAIL>
- **网站**: [https://fixguru.com](https://fixguru.com)

## 🚀 CI/CD 自动化部署

### 快速设置CI/CD流水线
```bash
# 1. 部署CI/CD流水线（一键完成）
./scripts/deploy-pipeline.sh dev

# 2. 推送代码触发自动构建
git add .
git commit -m "Setup CI/CD pipeline"
git push origin master

# 3. 监控Pipeline状态
./scripts/aws/manage-pipeline.sh status dev
```

### Pipeline管理命令
```bash
# 查看Pipeline状态
./scripts/aws/manage-pipeline.sh status dev

# 手动触发构建
./scripts/aws/manage-pipeline.sh start dev

# 查看构建日志
./scripts/aws/manage-pipeline.sh logs dev

# 查看执行历史
./scripts/aws/manage-pipeline.sh history dev
```

### 📋 CI/CD特性
- ✅ **自动触发**：代码推送到master分支自动构建部署
- ✅ **多环境支持**：dev/test/prod环境独立管理
- ✅ **安全管理**：GitHub Token和JWT密钥自动管理
- ✅ **构建缓存**：Maven依赖缓存加速构建
- ✅ **失败通知**：构建失败自动通知
- ✅ **一键回滚**：支持快速回滚到上一版本

### 📚 详细文档
- **CI/CD部署指南**: [docs/CICD部署指南.md](docs/CICD部署指南.md)
- **AWS部署指南**: [docs/AWS部署与开发指南.md](docs/AWS部署与开发指南.md)

## 📞 技术支持

如有问题，请通过以下方式联系：
- **邮箱**: <EMAIL>
- **文档**: 查看项目Wiki
- **问题反馈**: 提交GitHub Issue

## 📄 许可证

本项目采用 Apache 2.0 许可证。详见 [LICENSE](LICENSE) 文件。