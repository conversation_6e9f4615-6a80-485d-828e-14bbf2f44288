package com.fixguru.domain;

import com.fixguru.enums.UserStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey;

import java.time.Instant;

/**
 * 用户实体类
 * 对应DynamoDB中的User表
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@DynamoDbBean
public class UserDO extends BaseDO {

    /**
     * 用户ID（主键）
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 密码哈希
     */
    private String passwordHash;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 用户状态
     */
    private UserStatusEnum status = UserStatusEnum.ACTIVE;

    /**
     * 最后登录时间
     */
    private Instant lastLoginTime;

    /**
     * 锁定到期时间（仅LOCKED状态有效）
     */
    private Instant lockExpiresAt;

    /**
     * 登录失败次数
     */
    private Integer loginFailureCount;

    /**
     * 最后登录失败时间
     */
    private Instant lastLoginFailureTime;


    // DynamoDB属性映射方法（按字段声明顺序排列）
    @DynamoDbPartitionKey
    @DynamoDbAttribute("userId")
    public String getUserId() {
        return userId;
    }

    @DynamoDbAttribute("username")
    @DynamoDbSecondaryPartitionKey(indexNames = "username-index")
    public String getUsername() {
        return username;
    }

    @DynamoDbAttribute("email")
    @DynamoDbSecondaryPartitionKey(indexNames = "email-index")
    public String getEmail() {
        return email;
    }

    @DynamoDbAttribute("passwordHash")
    public String getPasswordHash() {
        return passwordHash;
    }

    @DynamoDbAttribute("avatarUrl")
    public String getAvatarUrl() {
        return avatarUrl;
    }

    @DynamoDbAttribute("status")
    public UserStatusEnum getStatus() {
        return status;
    }

    @DynamoDbAttribute("lastLoginTime")
    public Instant getLastLoginTime() {
        return lastLoginTime;
    }

    @DynamoDbAttribute("lockExpiresAt")
    public Instant getLockExpiresAt() {
        return lockExpiresAt;
    }

    @DynamoDbAttribute("loginFailureCount")
    public Integer getLoginFailureCount() {
        return loginFailureCount;
    }

    @DynamoDbAttribute("lastLoginFailureTime")
    public Instant getLastLoginFailureTime() {
        return lastLoginFailureTime;
    }

}
