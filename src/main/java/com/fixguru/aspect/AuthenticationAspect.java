package com.fixguru.aspect;

import com.fixguru.annotation.RequireAuth;
import com.fixguru.annotation.RequireOwnership;
import com.fixguru.constants.SystemConstants;
import com.fixguru.dto.UserInfoDTO;
import com.fixguru.enums.BizCodeEnum;
import com.fixguru.exception.BizException;
import com.fixguru.service.TokenService;
import com.fixguru.utils.TraceUtils;
import com.fixguru.utils.UserContextUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

/**
 * 认证切面
 * 处理@RequireAuth和@RequireOwnership注解
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
@Order(1) // 确保在其他切面之前执行
public class AuthenticationAspect {

    private final TokenService tokenService;

    /**
     * 处理@RequireAuth注解
     */
    @Before("@annotation(requireAuth)")
    public void handleRequireAuth(JoinPoint joinPoint, RequireAuth requireAuth) {
        log.debug("执行认证检查: {}", joinPoint.getSignature().toShortString());
        
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            throw new BizException(BizCodeEnum.UNAUTHORIZED, "Unable to get request context");
        }

        // 获取Authorization头
        String authHeader = request.getHeader(SystemConstants.SECURITY_AUTHORIZATION_HEADER);
        if (authHeader == null || !authHeader.startsWith(SystemConstants.JWT_HEADER_PREFIX)) {
            log.warn("认证失败: 缺少或无效的Authorization头 - Method: {}", joinPoint.getSignature().toShortString());
            throw new BizException(BizCodeEnum.UNAUTHORIZED, requireAuth.message());
        }

        // 提取并验证token
        String accessToken = authHeader.substring(SystemConstants.JWT_HEADER_PREFIX.length());
        if (!tokenService.validateAccessToken(accessToken)) {
            log.warn("认证失败: token无效或已过期 - Method: {}", joinPoint.getSignature().toShortString());
            throw new BizException(BizCodeEnum.UNAUTHORIZED, "Invalid or expired access token");
        }

        // 获取用户信息并存储到请求属性中
        UserInfoDTO userInfo = tokenService.getUserInfoFromAccessToken(accessToken);
        if (userInfo == null) {
            log.warn("认证失败: 无法获取用户信息 - Method: {}", joinPoint.getSignature().toShortString());
            throw new BizException(BizCodeEnum.UNAUTHORIZED, "Unable to extract user information from token");
        }

        // 存储用户信息到请求属性
        request.setAttribute("userInfo", userInfo);
        request.setAttribute("userId", userInfo.getUserId());

        // 将用户ID添加到MDC中，用于日志追踪
        TraceUtils.setUserId(userInfo.getUserId());

        log.debug("认证成功: userId={}, email={}, Method={}",
                userInfo.getUserId(), userInfo.getEmail(), joinPoint.getSignature().toShortString());
    }

    /**
     * 处理@RequireOwnership注解
     */
    @Before("@annotation(requireOwnership)")
    public void handleRequireOwnership(JoinPoint joinPoint, RequireOwnership requireOwnership) {
        log.debug("执行所有权检查: {}", joinPoint.getSignature().toShortString());
        
        // 获取当前用户ID
        String currentUserId = UserContextUtils.getCurrentUserId();
        if (currentUserId == null) {
            throw new BizException(BizCodeEnum.UNAUTHORIZED, "User not authenticated");
        }

        // 获取资源所有者ID
        String resourceOwnerId = extractResourceOwnerId(joinPoint, requireOwnership.value());
        if (resourceOwnerId == null) {
            log.warn("所有权检查失败: 无法获取资源所有者ID - Parameter: {}", requireOwnership.value());
            throw new BizException(BizCodeEnum.FORBIDDEN, "Unable to determine resource owner");
        }

        // 检查所有权
        if (!currentUserId.equals(resourceOwnerId)) {
            log.warn("所有权检查失败: 用户{}试图访问用户{}的资源 - Method: {}", 
                    currentUserId, resourceOwnerId, joinPoint.getSignature().toShortString());
            throw new BizException(BizCodeEnum.FORBIDDEN, requireOwnership.message());
        }

        log.debug("所有权检查通过: userId={}, Method={}", currentUserId, joinPoint.getSignature().toShortString());
    }

    /**
     * 从方法参数中提取资源所有者ID
     */
    private String extractResourceOwnerId(JoinPoint joinPoint, String parameterName) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Parameter[] parameters = method.getParameters();
        Object[] args = joinPoint.getArgs();

        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            
            // 检查@PathVariable注解
            PathVariable pathVariable = parameter.getAnnotation(PathVariable.class);
            if (pathVariable != null) {
                String pathVarName = pathVariable.value().isEmpty() ? pathVariable.name() : pathVariable.value();
                if (pathVarName.isEmpty()) {
                    pathVarName = parameter.getName();
                }
                if (parameterName.equals(pathVarName) && args[i] != null) {
                    return args[i].toString();
                }
            }
            
            // 检查@RequestParam注解
            RequestParam requestParam = parameter.getAnnotation(RequestParam.class);
            if (requestParam != null) {
                String requestParamName = requestParam.value().isEmpty() ? requestParam.name() : requestParam.value();
                if (requestParamName.isEmpty()) {
                    requestParamName = parameter.getName();
                }
                if (parameterName.equals(requestParamName) && args[i] != null) {
                    return args[i].toString();
                }
            }
            
            // 检查参数名
            if (parameterName.equals(parameter.getName()) && args[i] != null) {
                return args[i].toString();
            }
        }
        
        return null;
    }

    /**
     * 获取当前HTTP请求
     */
    private HttpServletRequest getCurrentRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes != null ? attributes.getRequest() : null;
    }
}
