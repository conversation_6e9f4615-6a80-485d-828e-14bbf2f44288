package com.fixguru.utils;

import com.fixguru.dto.UserInfoDTO;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 用户上下文工具类
 * 用于在Controller中获取当前登录用户信息
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public final class UserContextUtils {

    private static final String USER_INFO_ATTRIBUTE = "userInfo";
    private static final String USER_ID_ATTRIBUTE = "userId";

    private UserContextUtils() {
        // 工具类，禁止实例化
    }

    /**
     * 获取当前登录用户信息
     *
     * @return 用户信息，如果未登录则返回null
     */
    public static UserInfoDTO getCurrentUser() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return null;
        }
        return (UserInfoDTO) request.getAttribute(USER_INFO_ATTRIBUTE);
    }

    /**
     * 获取当前登录用户ID
     *
     * @return 用户ID，如果未登录则返回null
     */
    public static String getCurrentUserId() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return null;
        }
        return (String) request.getAttribute(USER_ID_ATTRIBUTE);
    }

    /**
     * 检查是否已登录
     *
     * @return 是否已登录
     */
    public static boolean isLoggedIn() {
        return getCurrentUserId() != null;
    }

    /**
     * 获取当前HTTP请求
     *
     * @return HttpServletRequest，如果不在请求上下文中则返回null
     */
    private static HttpServletRequest getCurrentRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes != null ? attributes.getRequest() : null;
    }
}
