package com.fixguru.utils;

import com.fixguru.config.JwtConfig;
import com.fixguru.constants.SystemConstants;
import com.fixguru.dto.UserInfoDTO;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类
 * 提供JWT令牌的生成、验证和解析功能
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtUtils {

    private final JwtConfig jwtConfig;

    /**
     * 获取签名密钥
     */
    private SecretKey getSigningKey() {
        return jwtConfig.getSigningKey();
    }

    /**
     * 生成Access Token
     *
     * @param userInfo 用户信息
     * @return JWT令牌
     */
    public String generateAccessToken(UserInfoDTO userInfo) {
        return generateToken(userInfo, jwtConfig.getAccessTokenExpirationSeconds(), "access");
    }

    /**
     * 生成Refresh Token
     *
     * @param userInfo 用户信息
     * @return JWT令牌
     */
    public String generateRefreshToken(UserInfoDTO userInfo) {
        return generateToken(userInfo, jwtConfig.getRefreshTokenExpirationSeconds(), "refresh");
    }

    /**
     * 生成JWT令牌
     *
     * @param userInfo          用户信息
     * @param expirationSeconds 过期时间（秒）
     * @param tokenType         令牌类型
     * @return JWT令牌
     */
    private String generateToken(UserInfoDTO userInfo, long expirationSeconds, String tokenType) {
        Instant now = Instant.now();
        Instant expiration = now.plusSeconds(expirationSeconds);

        Map<String, Object> claims = new HashMap<>();
        claims.put(SystemConstants.JWT_CLAIM_USER_ID, userInfo.getUserId());
        claims.put(SystemConstants.JWT_CLAIM_EMAIL, userInfo.getEmail());
        claims.put(SystemConstants.JWT_CLAIM_USERNAME, userInfo.getUsername());

        return tokenType + "_" + Jwts.builder()
                .claims(claims)
                .subject(SystemConstants.JWT_SUBJECT)
                .issuedAt(Date.from(now))
                .expiration(Date.from(expiration))
                .signWith(getSigningKey())
                .compact();
    }

    /**
     * 验证JWT令牌
     *
     * @param token JWT令牌
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            String actualToken = extractActualToken(token);
            if (actualToken == null) {
                return false;
            }

            Jwts.parser()
                    .verifyWith(getSigningKey())
                    .build()
                    .parseSignedClaims(actualToken);
            return true;
        } catch (JwtException | IllegalArgumentException e) {
            log.warn("JWT令牌验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 提取实际的JWT令牌（去除前缀）
     *
     * @param token 带前缀的令牌
     * @return 实际的JWT令牌
     */
    private String extractActualToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            return null;
        }

        // 检查是否有access_或refresh_前缀
        if (token.startsWith("access_")) {
            return token.substring(7);
        } else if (token.startsWith("refresh_")) {
            return token.substring(8);
        }

        // 如果没有前缀，直接返回原token（向后兼容）
        return token;
    }

    /**
     * 从JWT令牌中获取用户ID
     *
     * @param token JWT令牌
     * @return 用户ID
     */
    public String getUserIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? claims.get(SystemConstants.JWT_CLAIM_USER_ID, String.class) : null;
    }

    /**
     * 从JWT令牌中获取用户信息
     *
     * @param token JWT令牌
     * @return 用户信息
     */
    public UserInfoDTO getUserInfoFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        if (claims == null) {
            return null;
        }

        UserInfoDTO userInfo = new UserInfoDTO();
        userInfo.setUserId(claims.get(SystemConstants.JWT_CLAIM_USER_ID, String.class));
        userInfo.setEmail(claims.get(SystemConstants.JWT_CLAIM_EMAIL, String.class));
        userInfo.setUsername(claims.get(SystemConstants.JWT_CLAIM_USERNAME, String.class));
        return userInfo;
    }

    /**
     * 获取令牌过期时间
     *
     * @param token JWT令牌
     * @return 过期时间
     */
    public Instant getExpirationFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? claims.getExpiration().toInstant() : null;
    }

    /**
     * 检查令牌是否需要刷新
     *
     * @param token JWT令牌
     * @return 是否需要刷新
     */
    public boolean shouldRefreshToken(String token) {
        Instant expiration = getExpirationFromToken(token);
        if (expiration == null) {
            return false;
        }

        Instant now = Instant.now();
        long secondsUntilExpiration = expiration.getEpochSecond() - now.getEpochSecond();
        return secondsUntilExpiration <= SystemConstants.TOKEN_REFRESH_THRESHOLD_SECONDS;
    }

    /**
     * 从JWT令牌中获取令牌类型
     *
     * @param token JWT令牌
     * @return 令牌类型（access/refresh）
     */
    public String getTokenTypeFromToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            return null;
        }

        if (token.startsWith("access_")) {
            return "access";
        } else if (token.startsWith("refresh_")) {
            return "refresh";
        }

        return null;
    }

    /**
     * 从JWT令牌中获取Claims
     *
     * @param token JWT令牌
     * @return Claims
     */
    private Claims getClaimsFromToken(String token) {
        try {
            String actualToken = extractActualToken(token);
            if (actualToken == null) {
                return null;
            }

            return Jwts.parser()
                    .verifyWith(getSigningKey())
                    .build()
                    .parseSignedClaims(actualToken)
                    .getPayload();
        } catch (JwtException | IllegalArgumentException e) {
            log.warn("解析JWT令牌失败: {}", e.getMessage());
            return null;
        }
    }
}
