package com.fixguru.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.DynamoDbClientBuilder;

import java.net.URI;

/**
 * DynamoDB配置类
 * 根据不同环境配置DynamoDB客户端
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-07-27
 */
@Slf4j
@Configuration
public class DynamoDBConfig {

    @Value("${aws.region:us-west-1}")
    private String awsRegion;

    @Value("${aws.dynamodb.endpoint:}")
    private String dynamoDbEndpoint;

    @Value("${aws.dynamodb.access-key:}")
    private String accessKey;

    @Value("${aws.dynamodb.secret-key:}")
    private String secretKey;

    @Value("${aws.dynamodb.table-prefix:}")
    private String tablePrefix;

    /**
     * DynamoDB客户端配置
     */
    @Bean
    @Primary
    public DynamoDbClient dynamoDbClient() {
        try {
            DynamoDbClientBuilder builder = DynamoDbClient.builder().region(Region.of(awsRegion));

            // 检查Spring Profile来决定使用哪种配置
            String activeProfile = System.getProperty("spring.profiles.active",
                                  System.getenv("SPRING_PROFILES_ACTIVE"));

            // 如果是AWS环境（dev/test/prod），强制使用AWS DynamoDB
            if ("dev".equals(activeProfile) || "test".equals(activeProfile) || "prod".equals(activeProfile)) {
                log.info("配置DynamoDB AWS环境: region={}, profile={}", awsRegion, activeProfile);
                builder.credentialsProvider(DefaultCredentialsProvider.create());
            } else if (dynamoDbEndpoint != null && !dynamoDbEndpoint.isEmpty()) {
                log.info("配置DynamoDB本地环境: endpoint={}, region={}", dynamoDbEndpoint, awsRegion);
                builder.endpointOverride(URI.create(dynamoDbEndpoint))
                       .credentialsProvider(createLocalCredentialsProvider());
            } else {
                log.info("配置DynamoDB AWS环境: region={} (默认)", awsRegion);
                builder.credentialsProvider(DefaultCredentialsProvider.create());
            }

            return builder.build();
        } catch (Exception e) {
            log.warn("DynamoDB客户端配置失败，将使用模拟客户端: {}", e.getMessage());
            // 返回一个模拟的客户端，避免应用启动失败
            return createMockDynamoDbClient();
        }
    }

    /**
     * 创建模拟的DynamoDB客户端
     */
    private DynamoDbClient createMockDynamoDbClient() {
        log.info("创建模拟DynamoDB客户端");
        return DynamoDbClient.builder()
                .region(Region.of(awsRegion))
                .credentialsProvider(createLocalCredentialsProvider())
                .build();
    }

    /**
     * DynamoDB增强客户端配置
     */
    @Bean
    @Primary
    public DynamoDbEnhancedClient dynamoDbEnhancedClient(DynamoDbClient dynamoDbClient) {
        return DynamoDbEnhancedClient.builder()
                .dynamoDbClient(dynamoDbClient)
                .build();
    }

    /**
     * 表名前缀配置
     */
    @Bean
    public String tablePrefix() {
        return tablePrefix != null ? tablePrefix : "";
    }

    /**
     * 创建本地开发环境的凭证提供者
     */
    private AwsCredentialsProvider createLocalCredentialsProvider() {
        if (accessKey != null && !accessKey.isEmpty() && 
            secretKey != null && !secretKey.isEmpty()) {
            return StaticCredentialsProvider.create(
                AwsBasicCredentials.create(accessKey, secretKey)
            );
        }
        // 如果没有配置本地凭证，使用默认凭证提供者
        return DefaultCredentialsProvider.create();
    }
}
