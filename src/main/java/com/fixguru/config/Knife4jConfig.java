package com.fixguru.config;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.oas.models.tags.Tag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;

import java.util.ArrayList;
import java.util.List;

/**
 * Knife4j配置类
 * 配置API文档的基本信息和中文支持
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
@EnableKnife4j
@Profile({"dev", "test", "prod"})
public class Knife4jConfig {

    private final Environment environment;

    @Value("${server.servlet.context-path:/api}")
    private String contextPath;

    public Knife4jConfig(Environment environment) {
        this.environment = environment;
    }

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(getApiInfo())
                .servers(getServerList())
                .components(getComponents())
                .addSecurityItem(new SecurityRequirement().addList("FixGuru-Auth"))
                .tags(getApiTags());
    }

    /**
     * API基本信息
     */
    private Info getApiInfo() {
        return new Info()
                .title("FixGuru API 文档")
                .description(getApiDescription())
                .termsOfService("https://fixguruapp.com/terms")
                .contact(new Contact()
                        .name("FixGuru 开发团队")
                        .url("https://fixguruapp.com")
                        .email("<EMAIL>"))
                .version("1.0.0")
                .license(new License()
                        .name("Apache License 2.0")
                        .url("https://www.apache.org/licenses/LICENSE-2.0.html"));
    }

    /**
     * API描述信息
     */
    private String getApiDescription() {
        return """
                ## FixGuru API 接口文档

                基于AWS DynamoDB和Lambda的企业级维修服务平台。

                ### 🚀 主要功能
                - **用户管理**：用户注册、登录、信息查询和状态管理
                - **JWT认证**：基于注解的安全认证系统，支持access token和refresh token
                - **权限控制**：细粒度的资源访问控制，确保用户只能访问自己的资源
                - **Token管理**：支持token刷新和登出功能
                - **健康检查**：系统状态监控和健康检查接口

                ### 🔐 认证方式
                本API使用JWT Bearer Token进行身份验证：
                ```
                Authorization: FixGuru <access_token>
                ```

                ### 📋 接口分类
                - **公开接口**：无需认证即可访问（如注册、登录）
                - **认证接口**：需要有效的access token
                - **权限接口**：需要认证且只能访问自己的资源

                ### ⏰ Token有效期
                - **Access Token**：24小时（适合移动端长时间使用）
                - **Refresh Token**：30天（减少重新登录频率）

                ### 🌐 部署环境
                - **开发环境**：本地开发和测试
                - **AWS Lambda**：生产环境部署在AWS Lambda + API Gateway

                更多信息请访问我们的 [开发文档](https://fixguru.com/docs)。
                """;
    }

    /**
     * 安全组件配置
     */
    private Components getComponents() {
        return new Components()
                .addSecuritySchemes("FixGuru-Auth", new SecurityScheme()
                        .type(SecurityScheme.Type.HTTP)
                        .scheme("bearer")
                        .bearerFormat("JWT")
                        .name("FixGuru-Auth")
                        .description("FixGuru JWT认证\n\n" +
                                "请在请求头中添加：\n" +
                                "```\n" +
                                "Authorization: FixGuru <access_token>\n" +
                                "```\n\n" +
                                "获取access_token的方式：\n" +
                                "1. 调用登录接口获取\n" +
                                "2. 使用refresh_token刷新获取"));
    }

    /**
     * API标签分组
     */
    private List<Tag> getApiTags() {
        List<Tag> tags = new ArrayList<>();

        tags.add(new Tag()
                .name("用户认证")
                .description("用户注册、登录、登出和token管理相关接口"));

        tags.add(new Tag()
                .name("用户管理")
                .description("用户信息查询和状态管理接口（需要认证）"));

        tags.add(new Tag()
                .name("系统监控")
                .description("健康检查和系统状态监控接口"));

        return tags;
    }

    /**
     * 根据环境动态配置服务器列表
     */
    private List<Server> getServerList() {
        List<Server> servers = new ArrayList<>();

        // 检查是否在Lambda环境中运行
        boolean isLambda = System.getenv("AWS_LAMBDA_FUNCTION_NAME") != null;
        String[] activeProfiles = environment.getActiveProfiles();

        if (isLambda) {
            // Lambda环境配置
            String apiGatewayUrl = System.getenv("API_GATEWAY_URL");
            if (apiGatewayUrl != null && !apiGatewayUrl.isEmpty()) {
                servers.add(new Server()
                        .url(apiGatewayUrl)
                        .description("🚀 AWS Lambda 生产环境"));
            }

            // 添加备用Lambda URL
            String lambdaUrl = System.getenv("LAMBDA_FUNCTION_URL");
            if (lambdaUrl != null && !lambdaUrl.isEmpty()) {
                servers.add(new Server()
                        .url(lambdaUrl)
                        .description("⚡ AWS Lambda 函数URL"));
            }

            // 如果没有环境变量，添加默认的开发环境URL
            if (servers.isEmpty()) {
                servers.add(new Server()
                        .url("https://yjy4cjhi2e.execute-api.us-west-1.amazonaws.com/api")
                        .description("🔧 AWS Lambda 开发环境"));
            }
        } else {
            // 本地开发环境配置
            servers.add(new Server()
                    .url("http://localhost:8080" + contextPath)
                    .description("💻 本地开发环境"));

            // 添加可能的其他本地端口
            servers.add(new Server()
                    .url("http://localhost:8081" + contextPath)
                    .description("💻 本地开发环境 (备用端口)"));
        }

        return servers;
    }
}
