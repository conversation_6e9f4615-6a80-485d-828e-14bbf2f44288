package com.fixguru.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 需要资源所有权验证注解
 * 确保用户只能访问自己的资源
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireOwnership {
    
    /**
     * 资源所有者ID的参数名
     * 支持路径变量和请求参数
     */
    String value() default "userId";
    
    /**
     * 权限验证失败时的错误消息
     */
    String message() default "Access denied: You can only access your own resources";
}
