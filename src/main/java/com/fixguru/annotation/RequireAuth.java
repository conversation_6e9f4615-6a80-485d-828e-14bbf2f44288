package com.fixguru.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 需要认证注解
 * 标记需要access token认证的方法
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireAuth {
    
    /**
     * 认证失败时的错误消息
     */
    String message() default "Authentication required";
}
