package com.fixguru.service.impl;

import com.fixguru.config.JwtConfig;
import com.fixguru.constants.SystemConstants;
import com.fixguru.dto.AuthTokenDTO;
import com.fixguru.dto.UserInfoDTO;
import com.fixguru.enums.BizCodeEnum;
import com.fixguru.exception.BizException;
import com.fixguru.service.TokenService;
import com.fixguru.utils.JwtUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;

/**
 * 令牌服务实现类
 * 实现JWT令牌的生成、验证、刷新等功能
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TokenServiceImpl implements TokenService {

    private final JwtUtils jwtUtils;
    private final JwtConfig jwtConfig;

    @Override
    public AuthTokenDTO generateAuthToken(UserInfoDTO userInfo) {
        log.info("为用户生成认证令牌: userId={}", userInfo.getUserId());

        // 生成访问令牌和刷新令牌
        String accessToken = jwtUtils.generateAccessToken(userInfo);
        String refreshToken = jwtUtils.generateRefreshToken(userInfo);

        // 创建认证令牌DTO
        AuthTokenDTO authTokenDTO = new AuthTokenDTO();
        authTokenDTO.setAccessToken(accessToken);
        authTokenDTO.setRefreshToken(refreshToken);
        authTokenDTO.setExpiresIn(jwtConfig.getAccessTokenExpirationSeconds());
        authTokenDTO.setRefreshExpiresIn(jwtConfig.getRefreshTokenExpirationSeconds());
        authTokenDTO.setIssuedAt(Instant.now());
        authTokenDTO.setUserInfo(userInfo);

        log.info("认证令牌生成成功: userId={}, expiresIn={}s, refreshExpiresIn={}s",
                userInfo.getUserId(),
                authTokenDTO.getExpiresIn(),
                authTokenDTO.getRefreshExpiresIn());

        return authTokenDTO;
    }

    @Override
    public AuthTokenDTO refreshAccessToken(String refreshToken) {
        log.info("刷新访问令牌");

        // 验证刷新令牌
        if (!validateRefreshToken(refreshToken)) {
            log.warn("刷新令牌无效");
            throw new BizException(BizCodeEnum.REFRESH_TOKEN_INVALID);
        }

        // 从刷新令牌中获取用户信息
        UserInfoDTO userInfo = jwtUtils.getUserInfoFromToken(refreshToken);
        if (userInfo == null) {
            log.warn("无法从刷新令牌中获取用户信息");
            throw new BizException(BizCodeEnum.REFRESH_TOKEN_INVALID);
        }

        // 生成新的令牌对
        return generateAuthToken(userInfo);
    }

    @Override
    public boolean validateAccessToken(String accessToken) {
        if (accessToken == null || accessToken.trim().isEmpty()) {
            return false;
        }
        return jwtUtils.validateToken(accessToken);
    }

    @Override
    public boolean validateRefreshToken(String refreshToken) {
        if (refreshToken == null || refreshToken.trim().isEmpty()) {
            return false;
        }
        return jwtUtils.validateToken(refreshToken);
    }

    @Override
    public UserInfoDTO getUserInfoFromAccessToken(String accessToken) {
        if (!validateAccessToken(accessToken)) {
            return null;
        }
        return jwtUtils.getUserInfoFromToken(accessToken);
    }

    @Override
    public boolean shouldRefreshAccessToken(String accessToken) {
        if (!validateAccessToken(accessToken)) {
            return false;
        }
        return jwtUtils.shouldRefreshToken(accessToken);
    }

    @Override
    public Map<String, Object> logout(String accessToken, String refreshToken, String userId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 验证access token
            if (accessToken == null || !jwtUtils.validateToken(accessToken)) {
                log.warn("登出失败: 无效的access token, userId={}", userId);
                throw new BizException(BizCodeEnum.UNAUTHORIZED, "Invalid access token");
            }

            // 验证refresh token（如果提供）
            boolean refreshTokenValid = false;
            if (refreshToken != null && !refreshToken.trim().isEmpty()) {
                if (jwtUtils.validateToken(refreshToken)) {
                    UserInfoDTO refreshTokenUser = jwtUtils.getUserInfoFromToken(refreshToken);
                    if (refreshTokenUser != null && userId.equals(refreshTokenUser.getUserId())) {
                        refreshTokenValid = true;
                    } else {
                        log.warn("Refresh token不属于当前用户: userId={}", userId);
                    }
                } else {
                    log.warn("无效的refresh token: userId={}", userId);
                }
            }

            // 构建返回结果
            result.put("success", true);
            result.put("message", "Logout successful");
            result.put("accessTokenRevoked", true);
            result.put("refreshTokenRevoked", refreshTokenValid);
            result.put("timestamp", Instant.now());

            log.info("用户登出成功: userId={}, refreshTokenProvided={}", userId, refreshToken != null);

        } catch (Exception e) {
            log.error("用户登出失败: userId={}, error={}", userId, e.getMessage());
            result.put("success", false);
            result.put("error", e.getMessage());

            // 如果是业务异常，重新抛出
            if (e instanceof BizException) {
                throw e;
            }

            // 其他异常包装为业务异常
            throw new BizException(BizCodeEnum.LOGOUT_FAILED, "Logout failed: " + e.getMessage());
        }

        return result;
    }
}
