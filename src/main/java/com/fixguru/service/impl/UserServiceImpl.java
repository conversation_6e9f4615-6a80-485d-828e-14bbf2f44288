package com.fixguru.service.impl;

import com.fixguru.aws.dynamodb.DynamoDBService;
import com.fixguru.aws.dynamodb.GSIQueryService;
import com.fixguru.constants.SystemConstants;
import com.fixguru.domain.UserDO;
import com.fixguru.dto.AuthTokenDTO;
import com.fixguru.dto.UserInfoDTO;
import com.fixguru.dto.request.ForgotPasswordRequest;
import com.fixguru.dto.request.UserCreateRequest;
import com.fixguru.dto.request.UserLoginRequest;
import com.fixguru.enums.BizCodeEnum;
import com.fixguru.enums.UserStatusEnum;
import com.fixguru.exception.BizException;
import com.fixguru.service.TokenService;
import com.fixguru.service.UserService;
import com.fixguru.utils.IdUtils;
import com.fixguru.utils.JsonUtils;
import com.fixguru.utils.PasswordUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;

import java.time.Instant;
import java.util.Optional;

/**
 * 用户服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final DynamoDBService dynamoDBService;
    private final GSIQueryService gsiQueryService;
    private final TokenService tokenService;

    /**
     * 获取用户表
     */
    private DynamoDbTable<UserDO> getUserTable() {
        return dynamoDBService.getTable(UserDO.class, SystemConstants.TABLE_USER);
    }

    @Override
    public UserInfoDTO createUser(UserCreateRequest request) {

        // 验证同意条款
        if (request.getAgreeToTerms() == null || !request.getAgreeToTerms()) {
            throw new BizException(BizCodeEnum.TERMS_NOT_AGREED);
        }

        // 检查邮箱是否已存在
        if (existsByEmail(request.getEmail())) {
            throw new BizException(BizCodeEnum.EMAIL_ALREADY_EXISTS);
        }

        // 创建用户实体
        UserDO userDO = new UserDO();
        userDO.setUserId(IdUtils.generateUserId());
        // 使用firstName作为用户名，如果为空则使用邮箱前缀
        String username = request.getFirstName().replaceAll("\\s+", "").toLowerCase();
        if (username.isEmpty()) {
            username = request.getEmail().split("@")[0];
        }
        // 确保用户名唯一
        String finalUsername = ensureUniqueUsername(username);
        userDO.setUsername(finalUsername);
        userDO.setEmail(request.getEmail());
        userDO.setPasswordHash(PasswordUtils.hashPassword(request.getPassword()));
        userDO.setAvatarUrl(SystemConstants.DEFAULT_AVATAR_URL);
        userDO.setStatus(UserStatusEnum.ACTIVE);
        Instant now = Instant.now();
        userDO.setCreatedTime(now);
        userDO.setUpdatedTime(now);
        userDO.setCreatedBy("SYSTEM");
        userDO.setUpdatedBy("SYSTEM");
        userDO.setVersion(0L);
        // 保存用户
        DynamoDbTable<UserDO> table = getUserTable();
        dynamoDBService.save(table, userDO);

        log.info("用户注册成功: firstName={}, email={}, userId={}",
                request.getFirstName(), request.getEmail(), userDO.getUserId());

        // 异步记录注册事件（用于数据分析）
        recordUserRegistrationEventAsync(userDO, request);

        return convertToUserInfoDTO(userDO);
    }

    @Override
    public AuthTokenDTO login(UserLoginRequest request) {

        // 根据邮箱查找用户
        UserDO userDO = findUserByEmail(request.getEmail());
        if (userDO == null) {
            log.info("登录失败，用户不存在: email={}", request.getEmail());
            throw new BizException(BizCodeEnum.USER_NOT_FOUND);
        }

        // 检查用户是否可以登录
        if (!canUserLogin(userDO.getUserId())) {
            String errorMessage = getLoginErrorMessage(userDO.getStatus());
            log.info("登录失败，用户状态不允许登录: email={}, userId={}, status={}",
                    request.getEmail(), userDO.getUserId(), userDO.getStatus());
            throw new BizException(getLoginErrorCode(userDO.getStatus()), errorMessage);
        }

        // 验证密码
        if (!PasswordUtils.verifyPassword(request.getPassword(), userDO.getPasswordHash())) {
            // 记录登录失败
            handleLoginFailure(userDO);
            log.info("登录失败，密码错误: email={}, userId={}, failureCount={}",
                    request.getEmail(), userDO.getUserId(), userDO.getLoginFailureCount());
            throw new BizException(BizCodeEnum.INVALID_CREDENTIALS);
        }

        // 登录成功，更新最后登录时间，清除失败计数
        Instant now = Instant.now();
        userDO.setLastLoginTime(now);
        userDO.setLoginFailureCount(0);
        userDO.setLastLoginFailureTime(null);
        userDO.setUpdatedTime(now);
        userDO.setUpdatedBy(userDO.getUserId());

        DynamoDbTable<UserDO> table = getUserTable();
        dynamoDBService.save(table, userDO);

        // 生成JWT令牌
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setUserId(userDO.getUserId());
        userInfoDTO.setEmail(userDO.getEmail());
        userInfoDTO.setUsername(userDO.getUsername());

        AuthTokenDTO authTokenDTO = tokenService.generateAuthToken(userInfoDTO);
        authTokenDTO.setUserInfo(convertToUserInfoDTO(userDO));

        log.info("用户登录成功: email={}, userId={}, status={}",
                request.getEmail(), userDO.getUserId(), userDO.getStatus());
        return authTokenDTO;
    }

    @Override
    public UserInfoDTO getUserById(String userId) {
        DynamoDbTable<UserDO> table = getUserTable();
        Optional<UserDO> userOpt = dynamoDBService.findByKey(table, userId);

        if (userOpt.isEmpty() || userOpt.get().getDeleted()) {
            log.info("用户不存在: userId={}", userId);
            throw new BizException(BizCodeEnum.USER_NOT_FOUND);
        }

        UserDO user = userOpt.get();
        log.info("用户查询成功: userId={}, username={}", userId, user.getUsername());
        return convertToUserInfoDTO(user);
    }

    @Override
    public UserInfoDTO getUserByUsername(String username) {
        UserDO userDO = findUserByUsername(username);
        if (userDO == null) {
            log.info("用户不存在: username={}", username);
            throw new BizException(BizCodeEnum.USER_NOT_FOUND);
        }
        log.info("用户查询成功: username={}, userId={}", username, userDO.getUserId());
        return convertToUserInfoDTO(userDO);
    }

    @Override
    public UserInfoDTO getUserByEmail(String email) {
        UserDO userDO = findUserByEmail(email);
        if (userDO == null) {
            log.info("用户不存在: email={}", email);
            throw new BizException(BizCodeEnum.USER_NOT_FOUND);
        }
        log.info("用户查询成功: email={}, userId={}", email, userDO.getUserId());
        return convertToUserInfoDTO(userDO);
    }

    @Override
    public boolean existsByUsername(String username) {
        boolean exists = findUserByUsername(username) != null;
        log.info("用户名检查: username={}, exists={}", username, exists);
        return exists;
    }

    @Override
    public boolean existsByEmail(String email) {
        boolean exists = findUserByEmail(email) != null;
        log.info("邮箱检查: email={}, exists={}", email, exists);
        return exists;
    }


    @Override
    public void forgotPassword(ForgotPasswordRequest request) {
        // 检查用户是否存在
        UserDO userDO = findUserByEmail(request.getEmail());
        if (userDO == null) {
            // 为了安全考虑，即使用户不存在也返回成功，不暴露用户是否存在的信息
            log.info("忘记密码请求，用户不存在: email={}", request.getEmail());
            return;
        }

        // 检查用户状态
        if (userDO.getStatus() == UserStatusEnum.DISABLED) {
            log.info("忘记密码请求，用户已被禁用: email={}, userId={}", request.getEmail(), userDO.getUserId());
            return;
        }

        // TODO: 实际项目中应该：
        // 1. 生成重置密码令牌
        // 2. 发送重置密码邮件
        // 3. 设置令牌过期时间
        // 这里简化处理，只记录日志

        log.info("忘记密码请求处理成功: email={}, userId={}", request.getEmail(), userDO.getUserId());
    }

    /**
     * 确保用户名唯一
     */
    private String ensureUniqueUsername(String baseUsername) {
        String username = baseUsername;
        int counter = 1;

        while (existsByUsername(username)) {
            username = baseUsername + counter;
            counter++;
        }

        return username;
    }

    /**
     * 处理登录失败
     */
    private void handleLoginFailure(UserDO userDO) {
        // 更新登录失败次数和时间
        int failureCount = userDO.getLoginFailureCount() != null ? userDO.getLoginFailureCount() : 0;
        failureCount++;

        Instant now = Instant.now();
        userDO.setLoginFailureCount(failureCount);
        userDO.setLastLoginFailureTime(now);
        userDO.setUpdatedTime(now);
        userDO.setUpdatedBy("SYSTEM");

        // 保存失败记录
        DynamoDbTable<UserDO> table = getUserTable();
        dynamoDBService.save(table, userDO);

        log.warn("用户登录失败: userId={}, failureCount={}", userDO.getUserId(), failureCount);

        // TODO: 可以在这里添加自动锁定逻辑
        // if (failureCount >= SystemConstants.Security.MAX_LOGIN_ATTEMPTS) {
        //     lockUser(userDO.getUserId(), "登录失败次数过多，自动锁定账户");
        // }
    }

    /**
     * 获取登录错误码
     */
    private String getLoginErrorCode(UserStatusEnum status) {
        switch (status) {
            case LOCKED:
                return BizCodeEnum.USER_LOCKED.getCode();
            case DISABLED:
                return BizCodeEnum.USER_DISABLED.getCode();
            default:
                return BizCodeEnum.INVALID_CREDENTIALS.getCode();
        }
    }

    /**
     * 获取登录错误消息
     */
    private String getLoginErrorMessage(UserStatusEnum status) {
        switch (status) {
            case LOCKED:
                return BizCodeEnum.USER_LOCKED.getMessage();
            case DISABLED:
                return BizCodeEnum.USER_DISABLED.getMessage();
            default:
                return BizCodeEnum.INVALID_CREDENTIALS.getMessage();
        }
    }

    /**
     * 根据用户名查找用户
     * 使用GSI（全局二级索引）进行查询
     */
    private UserDO findUserByUsername(String username) {
        return gsiQueryService.findUserByUsername(username).orElse(null);
    }

    /**
     * 根据邮箱查找用户
     * 使用GSI（全局二级索引）进行查询
     */
    private UserDO findUserByEmail(String email) {
        return gsiQueryService.findUserByEmail(email).orElse(null);
    }

    /**
     * 根据用户ID查找用户
     */
    private UserDO findUserById(String userId) {
        DynamoDbTable<UserDO> table = getUserTable();
        return dynamoDBService.findByKey(table, userId).orElse(null);
    }

    /**
     * 转换为用户信息DTO
     */
    private UserInfoDTO convertToUserInfoDTO(UserDO userDO) {
        try {
            UserInfoDTO userInfoDTO = JsonUtils.convert(userDO, UserInfoDTO.class);
            return userInfoDTO;
        } catch (Exception e) {
            log.error("用户信息转换失败: userId={}, error={}", userDO.getUserId(), e.getMessage(), e);
            // 如果转换失败，手动创建响应对象
            return createUserInfoDTOManually(userDO);
        }
    }

    /**
     * 手动创建用户信息DTO（备用方法）
     */
    private UserInfoDTO createUserInfoDTOManually(UserDO userDO) {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setUserId(userDO.getUserId());
        userInfoDTO.setUsername(userDO.getUsername());
        userInfoDTO.setEmail(userDO.getEmail());
        userInfoDTO.setAvatarUrl(userDO.getAvatarUrl());
        userInfoDTO.setStatus(userDO.getStatus());
        userInfoDTO.setLastLoginTime(userDO.getLastLoginTime());
        userInfoDTO.setCreatedTime(userDO.getCreatedTime());
        userInfoDTO.setUpdatedTime(userDO.getUpdatedTime());
        return userInfoDTO;
    }


    @Override
    public boolean lockUser(String userId, String reason) {
        UserDO userDO = findUserById(userId);
        if (userDO == null) {
            log.warn("锁定用户失败，用户不存在: userId={}", userId);
            return false;
        }

        // 检查当前状态是否可以锁定
        if (!userDO.getStatus().canTransitionTo(UserStatusEnum.LOCKED)) {
            log.warn("锁定用户失败，状态不允许转换: userId={}, currentStatus={}",
                    userId, userDO.getStatus());
            return false;
        }

        return changeUserStatus(userDO, UserStatusEnum.LOCKED, reason);
    }

    @Override
    public boolean unlockUser(String userId, String reason) {
        UserDO userDO = findUserById(userId);
        if (userDO == null) {
            log.warn("解锁用户失败，用户不存在: userId={}", userId);
            return false;
        }

        if (userDO.getStatus() != UserStatusEnum.LOCKED) {
            log.warn("解锁用户失败，用户未被锁定: userId={}, currentStatus={}",
                    userId, userDO.getStatus());
            return false;
        }

        return changeUserStatus(userDO, UserStatusEnum.ACTIVE, reason);
    }

    @Override
    public boolean disableUser(String userId, String adminId, String reason) {
        UserDO userDO = findUserById(userId);
        if (userDO == null) {
            log.warn("禁用用户失败，用户不存在: userId={}", userId);
            return false;
        }

        // 检查当前状态是否可以禁用
        if (!userDO.getStatus().canTransitionTo(UserStatusEnum.DISABLED)) {
            log.warn("禁用用户失败，状态不允许转换: userId={}, currentStatus={}",
                    userId, userDO.getStatus());
            return false;
        }

        return changeUserStatus(userDO, UserStatusEnum.DISABLED, reason);
    }

    @Override
    public boolean enableUser(String userId, String adminId, String reason) {
        UserDO userDO = findUserById(userId);
        if (userDO == null) {
            log.warn("启用用户失败，用户不存在: userId={}", userId);
            return false;
        }

        if (userDO.getStatus() != UserStatusEnum.DISABLED) {
            log.warn("启用用户失败，用户未被禁用: userId={}, currentStatus={}",
                    userId, userDO.getStatus());
            return false;
        }

        return changeUserStatus(userDO, UserStatusEnum.ACTIVE, reason);
    }

    @Override
    public boolean canUserLogin(String userId) {
        UserDO userDO = findUserById(userId);
        if (userDO == null) {
            return false;
        }
        return userDO.getStatus().isCanLogin();
    }

    /**
     * 变更用户状态
     */
    private boolean changeUserStatus(UserDO userDO, UserStatusEnum newStatus, String reason) {
        UserStatusEnum oldStatus = userDO.getStatus();
        if (oldStatus == newStatus) {
            log.info("用户状态未变更: userId={}, currentStatus={}", userDO.getUserId(), oldStatus);
            return false;
        }
        userDO.setStatus(newStatus);
        userDO.setUpdatedTime(Instant.now());
        userDO.setUpdatedBy("SYSTEM");

        // 保存用户信息
        DynamoDbTable<UserDO> userTable = getUserTable();
        dynamoDBService.save(userTable, userDO);

        log.info("用户状态变更成功: userId={}, {} -> {}, reason={}",
                userDO.getUserId(), oldStatus, newStatus, reason);

        return true;
    }

    /**
     * 异步记录用户注册事件（用于数据分析）
     *
     * @param userDO  用户实体
     * @param request 注册请求
     */
    @Async
    protected void recordUserRegistrationEventAsync(UserDO userDO, UserCreateRequest request) {
        try {
            // 记录注册事件的详细信息
            log.info("记录用户注册事件: userId={}, email={}, firstName={}, registrationTime={}, subscribeUpdates={}",
                    userDO.getUserId(),
                    userDO.getEmail(),
                    request.getFirstName(),
                    userDO.getCreatedTime(),
                    request.getSubscribeUpdates());

            // TODO: 这里可以扩展更多数据分析相关的逻辑，例如：
            // 1. 发送注册事件到数据分析系统（如AWS Kinesis、CloudWatch等）
            // 2. 记录用户注册来源、设备信息等
            // 3. 触发欢迎邮件发送
            // 4. 更新用户统计数据
            // 5. 记录到专门的事件表中

            // 模拟异步处理时间
            Thread.sleep(100);

            log.info("用户注册事件记录完成: userId={}", userDO.getUserId());

        } catch (Exception e) {
            // 异步处理失败不应该影响主流程，只记录错误日志
            log.error("记录用户注册事件失败: userId={}, error={}", userDO.getUserId(), e.getMessage(), e);
        }
    }
}
