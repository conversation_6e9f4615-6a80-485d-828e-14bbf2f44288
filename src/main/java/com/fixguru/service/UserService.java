package com.fixguru.service;

import com.fixguru.dto.AuthTokenDTO;
import com.fixguru.dto.UserInfoDTO;
import com.fixguru.dto.request.ForgotPasswordRequest;
import com.fixguru.dto.request.UserCreateRequest;
import com.fixguru.dto.request.UserLoginRequest;

/**
 * 用户服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface UserService {

    /**
     * 创建用户
     *
     * @param request 用户创建请求
     * @return 用户信息响应
     */
    UserInfoDTO createUser(UserCreateRequest request);

    /**
     * 用户登录
     *
     * @param request 用户登录请求
     * @return 认证令牌响应
     */
    AuthTokenDTO login(UserLoginRequest request);

    /**
     * 根据用户ID获取用户信息
     *
     * @param userId 用户ID
     * @return 用户信息响应
     */
    UserInfoDTO getUserById(String userId);

    /**
     * 根据用户名获取用户信息
     *
     * @param username 用户名
     * @return 用户信息响应
     */
    UserInfoDTO getUserByUsername(String username);

    /**
     * 根据邮箱获取用户信息
     *
     * @param email 邮箱
     * @return 用户信息响应
     */
    UserInfoDTO getUserByEmail(String email);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @return 是否存在
     */
    boolean existsByEmail(String email);


    /**
     * 忘记密码
     *
     * @param request 忘记密码请求
     */
    void forgotPassword(ForgotPasswordRequest request);


    /**
     * 锁定用户账户
     *
     * @param userId 用户ID
     * @param reason 锁定原因
     * @return 是否锁定成功
     */
    boolean lockUser(String userId, String reason);

    /**
     * 解锁用户账户
     *
     * @param userId 用户ID
     * @param reason 解锁原因
     * @return 是否解锁成功
     */
    boolean unlockUser(String userId, String reason);

    /**
     * 禁用用户账户
     *
     * @param userId  用户ID
     * @param adminId 管理员ID
     * @param reason  禁用原因
     * @return 是否禁用成功
     */
    boolean disableUser(String userId, String adminId, String reason);

    /**
     * 启用用户账户
     *
     * @param userId  用户ID
     * @param adminId 管理员ID
     * @param reason  启用原因
     * @return 是否启用成功
     */
    boolean enableUser(String userId, String adminId, String reason);

    /**
     * 检查用户是否可以登录
     *
     * @param userId 用户ID
     * @return 是否可以登录
     */
    boolean canUserLogin(String userId);
}
