package com.fixguru.constants;

/**
 * 系统常量类
 * 统一管理系统中使用的各种常量
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public final class SystemConstants {

    private SystemConstants() {
        // 工具类，禁止实例化
    }

    /**
     * HTTP状态码常量
     */
    public static final String HTTP_SUCCESS = "200";
    public static final String HTTP_BAD_REQUEST = "400";
    public static final String HTTP_UNAUTHORIZED = "401";
    public static final String HTTP_FORBIDDEN = "403";
    public static final String HTTP_NOT_FOUND = "404";
    public static final String HTTP_INTERNAL_ERROR = "500";
    public static final String HTTP_SERVICE_UNAVAILABLE = "503";

    /**
     * 缓存键常量
     */
    public static final String CACHE_USER_PREFIX = "user:";
    public static final String CACHE_TOKEN_PREFIX = "token:";
    public static final String CACHE_CAPTCHA_PREFIX = "captcha:";
    public static final String CACHE_RATE_LIMIT_PREFIX = "rate_limit:";

    /**
     * 客户端类型常量
     */
    public static final String CLIENT_TYPE_IOS = "iOS";
    public static final String CLIENT_TYPE_ANDROID = "Android";
    public static final String CLIENT_TYPE_WEB = "Web";
    public static final String CLIENT_TYPE_API = "API";

    /**
     * DynamoDB表名常量
     */
    public static final String TABLE_USER = "User";

    /**
     * 时间相关常量
     */
    /** 移动端APP适配的令牌有效期 - 24小时，适合移动端长时间使用 */
    public static final long TOKEN_EXPIRE_SECONDS = 86400L;
    /** 刷新令牌有效期 - 30天，减少重新登录频率 */
    public static final long REFRESH_TOKEN_EXPIRE_SECONDS = 2592000L;
    /** 验证码有效期 - 5分钟 */
    public static final long CAPTCHA_EXPIRE_SECONDS = 300L;
    /** 令牌刷新时机 - Access Token还有2小时过期时自动刷新 */
    public static final long TOKEN_REFRESH_THRESHOLD_SECONDS = 7200L;

    /**
     * 文件相关常量
     */
    public static final String FILE_AVATAR_PATH = "avatars/";
    public static final String FILE_TEMP_PATH = "temp/";
    /** 文件最大大小 - 10MB */
    public static final long FILE_MAX_SIZE = 10 * 1024 * 1024L;

    /**
     * JWT相关常量
     */
    public static final String JWT_SUBJECT = "FixGuru";
    public static final String JWT_HEADER_PREFIX = "FixGuru ";
    public static final String JWT_CLAIM_USER_ID = "userId";
    public static final String JWT_CLAIM_EMAIL = "email";
    public static final String JWT_CLAIM_USERNAME = "username";

    /**
     * 正则表达式常量
     */
    public static final String REGEX_EMAIL = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";
    public static final String REGEX_PASSWORD = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[a-zA-Z\\d@$!%*?&]{8,}$";

    /**
     * 默认值常量
     */
    public static final String DEFAULT_AVATAR_URL = "https://example.com/default-avatar.png";
    public static final String DEFAULT_NICKNAME = "用户";
    public static final int DEFAULT_PAGE_SIZE = 20;
    public static final int DEFAULT_MAX_PAGE_SIZE = 100;
    public static final String DEFAULT_TIMEZONE = "UTC";
    public static final String DEFAULT_LOCALE = "en_US";

    /**
     * 业务规则常量
     */
    public static final int BUSINESS_MAX_RETRY_ATTEMPTS = 3;
    public static final long BUSINESS_RETRY_DELAY_MS = 1000;
    /** DynamoDB批量操作限制 */
    public static final int BUSINESS_MAX_BATCH_SIZE = 25;
    public static final int BUSINESS_MIN_PASSWORD_LENGTH = 8;
    public static final int BUSINESS_MAX_PASSWORD_LENGTH = 32;
    public static final int BUSINESS_MAX_USERNAME_LENGTH = 50;
    public static final int BUSINESS_MAX_EMAIL_LENGTH = 100;

    /**
     * 安全相关常量
     */
    public static final String SECURITY_AUTHORIZATION_HEADER = "Authorization";
    public static final String SECURITY_BEARER_PREFIX = "Bearer ";
    public static final int SECURITY_MAX_LOGIN_ATTEMPTS = 5;
    public static final int SECURITY_ACCOUNT_LOCK_MINUTES = 30;
    public static final String SECURITY_SYSTEM_USER = "system";

    /**
     * API相关常量
     */
    public static final String API_VERSION = "v1";
    public static final String API_PREFIX = "/api/" + API_VERSION;
    public static final String API_HEALTH_ENDPOINT = "/health";
    public static final String API_METRICS_ENDPOINT = "/metrics";
}
