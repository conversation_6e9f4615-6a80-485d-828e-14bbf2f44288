package com.fixguru.aws.dynamodb;

import com.fixguru.domain.UserDO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * GSI查询服务
 * 演示如何使用全局二级索引进行查询
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-07-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GSIQueryService {

    private final DynamoDbEnhancedClient enhancedClient;
    private final String tablePrefix;

    /**
     * 根据用户名查询用户（使用GSI）
     * 
     * @param username 用户名
     * @return 用户信息
     */
    public Optional<UserDO> findUserByUsername(String username) {
        try {
            // 获取用户表
            DynamoDbTable<UserDO> table = getUserTable();

            // 获取用户名GSI
            DynamoDbIndex<UserDO> usernameIndex = table.index("username-index");

            // 构建查询条件
            QueryConditional queryConditional = QueryConditional.keyEqualTo(
                Key.builder().partitionValue(username).build()
            );

            // 执行查询，获取所有匹配的用户
            List<UserDO> users = usernameIndex.query(
                QueryEnhancedRequest.builder()
                    .queryConditional(queryConditional)
                    .build()
            ).stream()
             .flatMap(page -> page.items().stream())
             .collect(Collectors.toList());

            if (users.isEmpty()) {
                return Optional.empty();
            }

            // 查找第一个未删除的用户
            Optional<UserDO> activeUser = users.stream()
                    .filter(user -> !Boolean.TRUE.equals(user.getDeleted()))
                    .findFirst();

            if (activeUser.isEmpty()) {
                return Optional.empty();
            }

            UserDO user = activeUser.get();
            log.info("GSI查询成功: username={} -> userId={}", username, user.getUserId());

            return Optional.of(user);

        } catch (Exception e) {
            log.error("GSI查询失败: username={}, error={}", username, e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * 根据邮箱查询用户（使用GSI）
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    public Optional<UserDO> findUserByEmail(String email) {
        try {
            // 获取用户表
            DynamoDbTable<UserDO> table = getUserTable();

            // 获取邮箱GSI
            DynamoDbIndex<UserDO> emailIndex = table.index("email-index");

            // 构建查询条件
            QueryConditional queryConditional = QueryConditional.keyEqualTo(
                Key.builder().partitionValue(email).build()
            );

            // 执行查询，获取所有匹配的用户
            List<UserDO> users = emailIndex.query(
                QueryEnhancedRequest.builder()
                    .queryConditional(queryConditional)
                    .build()
            ).stream()
             .flatMap(page -> page.items().stream())
             .collect(Collectors.toList());

            if (users.isEmpty()) {
                return Optional.empty();
            }

            // 查找第一个未删除的用户
            Optional<UserDO> activeUser = users.stream()
                    .filter(user -> !Boolean.TRUE.equals(user.getDeleted()))
                    .findFirst();

            if (activeUser.isEmpty()) {
                return Optional.empty();
            }

            UserDO user = activeUser.get();
            log.info("GSI查询成功: email={} -> userId={}", email, user.getUserId());

            return Optional.of(user);

        } catch (Exception e) {
            log.error("GSI查询失败: email={}, error={}", email, e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * 获取用户表
     */
    private DynamoDbTable<UserDO> getUserTable() {
        String tableName = tablePrefix + "User";
        return enhancedClient.table(tableName, TableSchema.fromBean(UserDO.class));
    }
}
