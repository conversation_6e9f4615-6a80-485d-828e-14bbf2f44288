package com.fixguru.controller;

import com.fixguru.annotation.RequireAuth;
import com.fixguru.annotation.RequireOwnership;
import com.fixguru.common.ApiResponse;
import com.fixguru.constants.SystemConstants;
import com.fixguru.dto.AuthTokenDTO;
import com.fixguru.dto.UserInfoDTO;
import com.fixguru.dto.request.ForgotPasswordRequest;
import com.fixguru.dto.request.UserCreateRequest;
import com.fixguru.dto.request.UserLoginRequest;
import com.fixguru.enums.BizCodeEnum;
import com.fixguru.service.TokenService;
import com.fixguru.service.UserService;
import com.fixguru.utils.UserContextUtils;
import jakarta.servlet.http.HttpServletRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户控制器
 * 提供用户相关的API接口
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/v1/users")
@RequiredArgsConstructor
@Validated
@Tag(name = "用户认证", description = "用户注册、登录、登出和token管理")
public class UserController {

    private final UserService userService;
    private final TokenService tokenService;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @Operation(
            summary = "用户注册",
            description = "创建新用户账户，无需认证。注册成功后返回用户基本信息。",
            tags = {"用户认证"}
    )
    public ApiResponse<UserInfoDTO> register(@Valid @RequestBody UserCreateRequest request) {
        log.info("用户注册请求: firstName={}, email={}", request.getFirstName(), request.getEmail());
        UserInfoDTO userInfoDTO = userService.createUser(request);
        return ApiResponse.success(BizCodeEnum.REGISTRATION_SUCCESS.getMessage(), userInfoDTO);
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Operation(
            summary = "用户登录",
            description = "用户登录获取访问令牌和刷新令牌，无需认证。返回的token用于后续API调用。",
            tags = {"用户认证"}
    )
    public ApiResponse<AuthTokenDTO> login(@Valid @RequestBody UserLoginRequest request) {
        log.info("用户登录请求: email={}", request.getEmail());
        AuthTokenDTO authTokenDTO = userService.login(request);
        return ApiResponse.success(BizCodeEnum.LOGIN_SUCCESS.getMessage(), authTokenDTO);
    }

    /**
     * 忘记密码
     */
    @PostMapping("/forgot-password")
    @Operation(
            summary = "忘记密码",
            description = "发送重置密码邮件，无需认证。用户可通过邮件重置密码。",
            tags = {"用户认证"}
    )
    public ApiResponse<Void> forgotPassword(@Valid @RequestBody ForgotPasswordRequest request) {
        log.info("忘记密码请求: email={}", request.getEmail());
        userService.forgotPassword(request);
        return ApiResponse.success(BizCodeEnum.PASSWORD_RESET_EMAIL_SENT.getMessage(), null);
    }

    /**
     * 刷新访问令牌
     */
    @PostMapping("/refresh-token")
    @Operation(
            summary = "刷新访问令牌",
            description = "使用刷新令牌获取新的访问令牌，无需认证。当access token过期时使用。",
            tags = {"用户认证"}
    )
    public ApiResponse<AuthTokenDTO> refreshToken(
            @Parameter(description = "刷新令牌", required = true)
            @RequestParam @NotBlank(message = "Refresh token is required") String refreshToken) {
        log.info("刷新令牌请求");
        AuthTokenDTO authTokenDTO = tokenService.refreshAccessToken(refreshToken);
        return ApiResponse.success(BizCodeEnum.TOKEN_REFRESHED.getMessage(), authTokenDTO);
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @Operation(
            summary = "用户登出",
            description = "用户登出，验证token有效性并记录登出操作。客户端应清除本地存储的token。",
            tags = {"用户认证"},
            security = @SecurityRequirement(name = "FixGuru-Auth")
    )
    @RequireAuth(message = "Authentication required for logout")
    public ApiResponse<Map<String, Object>> logout(
            @Parameter(description = "刷新令牌（可选）")
            @RequestParam(required = false) String refreshToken,
            HttpServletRequest request) {

        String currentUserId = UserContextUtils.getCurrentUserId();

        // 从请求头中获取access token
        String authHeader = request.getHeader(SystemConstants.SECURITY_AUTHORIZATION_HEADER);
        String accessToken = authHeader.substring(SystemConstants.JWT_HEADER_PREFIX.length());

        log.info("用户登出请求: userId={}", currentUserId);

        // 执行登出逻辑
        Map<String, Object> logoutResult = tokenService.logout(accessToken, refreshToken, currentUserId);

        return ApiResponse.success(BizCodeEnum.LOGOUT_SUCCESS.getMessage(), logoutResult);
    }

    /**
     * 根据用户ID获取用户信息
     * 注意：由于权限控制，用户只能查看自己的信息
     */
    @GetMapping("/{userId}")
    @Operation(
            summary = "获取用户信息",
            description = "根据用户ID获取用户详细信息。由于权限控制，用户只能查看自己的信息。",
            tags = {"用户管理"},
            security = @SecurityRequirement(name = "FixGuru-Auth")
    )
    @RequireAuth
    @RequireOwnership("userId")
    public ApiResponse<UserInfoDTO> getUserById(
            @Parameter(description = "用户ID（必须是当前登录用户的ID）", required = true)
            @PathVariable @NotBlank(message = "User ID is required") String userId) {

        // 由于@RequireOwnership验证，这里的userId一定是当前用户的ID
        UserInfoDTO currentUser = UserContextUtils.getCurrentUser();
        if (currentUser != null) {
            log.debug("直接返回当前用户信息: userId={}", currentUser.getUserId());
            return ApiResponse.success(currentUser);
        }

        // 兜底方案：如果无法从上下文获取，则查询数据库
        UserInfoDTO userInfoDTO = userService.getUserById(userId);
        return ApiResponse.success(userInfoDTO);
    }

    /**
     * 根据用户名获取用户信息
     * 注意：此接口允许查询任何用户的信息，仅用于系统内部功能
     */
    @GetMapping("/username/{username}")
    @Operation(
            summary = "根据用户名获取用户信息",
            description = "根据用户名获取用户详细信息，需要认证。此接口允许查询任何用户的信息，仅用于系统内部功能。",
            tags = {"用户管理"},
            security = @SecurityRequirement(name = "FixGuru-Auth")
    )
    @RequireAuth
    public ApiResponse<UserInfoDTO> getUserByUsername(
            @Parameter(description = "用户名", required = true)
            @PathVariable @NotBlank(message = "Username is required") String username) {
        UserInfoDTO userInfoDTO = userService.getUserByUsername(username);
        return ApiResponse.success(userInfoDTO);
    }

    /**
     * 根据邮箱获取用户信息
     * 注意：此接口允许查询任何用户的信息，仅用于系统内部功能
     */
    @GetMapping("/email/{email}")
    @Operation(
            summary = "根据邮箱获取用户信息",
            description = "根据邮箱获取用户详细信息，需要认证。此接口允许查询任何用户的信息，仅用于系统内部功能。",
            tags = {"用户管理"},
            security = @SecurityRequirement(name = "FixGuru-Auth")
    )
    @RequireAuth
    public ApiResponse<UserInfoDTO> getUserByEmail(
            @Parameter(description = "邮箱", required = true)
            @PathVariable @NotBlank(message = "Email is required") String email) {
        UserInfoDTO userInfoDTO = userService.getUserByEmail(email);
        return ApiResponse.success(userInfoDTO);
    }

    /**
     * 检查邮箱是否存在
     */
    @GetMapping("/check/email/{email}")
    @Operation(
            summary = "检查邮箱是否存在",
            description = "检查指定邮箱是否已被使用，无需认证。用于注册时验证邮箱可用性。",
            tags = {"用户认证"}
    )
    public ApiResponse<Boolean> checkEmailExists(
            @Parameter(description = "邮箱", required = true)
            @PathVariable @NotBlank(message = "Email is required") String email) {
        boolean exists = userService.existsByEmail(email);
        return ApiResponse.success(exists);
    }


    /**
     * 解锁用户账户
     * 注意：此接口需要管理员权限，当前已禁用
     */
//    @PostMapping("/{userId}/unlock")
//    @Operation(summary = "解锁用户账户", description = "解锁被锁定的用户账户")
//    @RequireAuth
//    @RequireAdminRole  // 未来如果需要管理员权限，可以添加此注解
    public ApiResponse<Boolean> unlockUser(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotBlank(message = "User ID is required") String userId,
            @Parameter(description = "解锁原因")
            @RequestParam(required = false, defaultValue = "Admin unlock") String reason) {
        boolean success = userService.unlockUser(userId, reason);
        return ApiResponse.success(success ? BizCodeEnum.ACCOUNT_UNLOCKED.getMessage() : "Unlock failed", success);
    }

    /**
     * 检查用户状态
     */
    @GetMapping("/{userId}/status")
    @Operation(
            summary = "检查用户状态",
            description = "检查用户当前状态和权限，需要认证。用户只能查看自己的状态信息。",
            tags = {"用户管理"},
            security = @SecurityRequirement(name = "FixGuru-Auth")
    )
    @RequireAuth
    @RequireOwnership("userId")
    public ApiResponse<Map<String, Object>> checkUserStatus(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotBlank(message = "User ID is required") String userId) {

        UserInfoDTO currentUser = UserContextUtils.getCurrentUser();

        if (currentUser != null) {
            // 直接使用上下文中的用户信息，避免重复查询
            boolean canLogin = userService.canUserLogin(userId);

            Map<String, Object> statusInfo = new HashMap<>();
            statusInfo.put("status", currentUser.getStatus());
            statusInfo.put("canLogin", canLogin);

            log.debug("返回当前用户状态: userId={}, status={}", userId, currentUser.getStatus());
            return ApiResponse.success(statusInfo);
        }

        // 兜底方案：如果无法从上下文获取，则查询数据库
        UserInfoDTO userInfoDTO = userService.getUserById(userId);
        boolean canLogin = userService.canUserLogin(userId);

        Map<String, Object> statusInfo = new HashMap<>();
        statusInfo.put("status", userInfoDTO.getStatus());
        statusInfo.put("canLogin", canLogin);

        return ApiResponse.success(statusInfo);
    }
}
