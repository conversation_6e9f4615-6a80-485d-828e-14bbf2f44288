# 生产环境配置 (AWS Lambda + DynamoDB)
# 继承 application.yml 的基础配置，只覆盖需要修改的部分
# 注意：Lambda环境不需要server.port配置，由AWS Lambda Runtime管理

# AWS配置 - 生产环境
aws:
  dynamodb:
    table-prefix: "prod_"

# 应用配置 - 生产环境特定
app:
  basic:
    name: FixGuru

  security:
    jwt-secret: ${JWT_SECRET}  # 生产环境必须通过环境变量设置

  allowed-origins:
    - "https://fixguru.com"
    - "https://admin.fixguru.com"
    - "https://app.fixguru.com"

# 日志配置 - 生产环境（最小日志）
logging:
  level:
    com.fixguru: WARN
    org.springframework: ERROR
    software.amazon.awssdk: ERROR
    io.swagger: ERROR

# Knife4j配置 - 生产环境禁用
knife4j:
  enable: false
  production: true

# Lambda优化配置
spring:
  main:
    lazy-initialization: true
