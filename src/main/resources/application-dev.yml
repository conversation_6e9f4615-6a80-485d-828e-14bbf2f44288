# 开发环境配置
# 用于本地开发（连接AWS DynamoDB）+ AWS开发环境部署

# 服务器配置 - 仅本地开发需要
server:
  port: ${SERVER_PORT:8080}  # 支持环境变量覆盖，默认8080

# AWS配置 - 开发环境
aws:
  dynamodb:
    table-prefix: ${TABLE_PREFIX:dev_}

# 应用配置 - 开发环境特定
app:
  basic:
    name: FixGuru Dev

  security:
    jwt-secret: ${JWT_SECRET:FixGuru-Dev-JWT-Secret-Key-2024-Please-Change}

# 日志配置 - 开发环境（更详细的日志）
logging:
  level:
    com.fixguru: INFO
    org.springframework.web: INFO
    software.amazon.awssdk: INFO
