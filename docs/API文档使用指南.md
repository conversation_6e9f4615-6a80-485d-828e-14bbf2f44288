# FixGuru API 文档使用指南

## 📖 文档访问地址

### 开发环境
- **本地开发**: http://localhost:8080/api/doc.html
- **AWS Lambda开发环境**: https://yjy4cjhi2e.execute-api.us-west-1.amazonaws.com/api/doc.html

### 生产环境
- 生产环境默认禁用API文档（安全考虑）
- 如需访问，请联系管理员

## 🚀 快速开始

### 1. 用户注册和登录流程

#### 步骤1：用户注册
```http
POST /api/v1/users/register
Content-Type: application/json

{
  "firstName": "张",
  "lastName": "三",
  "email": "<EMAIL>",
  "password": "password123",
  "phoneNumber": "+86-13800138000"
}
```

#### 步骤2：用户登录
```http
POST /api/v1/users/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应示例：**
```json
{
  "code": "2000",
  "message": "Login successful",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiJ9...",
    "tokenType": "FixGuru",
    "expiresIn": 86400,
    "refreshExpiresIn": 2592000
  }
}
```

#### 步骤3：使用Access Token访问受保护的接口
```http
GET /api/v1/users/{userId}
Authorization: FixGuru eyJhbGciOiJIUzI1NiJ9...
```

### 2. Token管理

#### 刷新Access Token
```http
POST /api/v1/users/refresh-token
Content-Type: application/x-www-form-urlencoded

refreshToken=eyJhbGciOiJIUzI1NiJ9...
```

#### 用户登出
```http
POST /api/v1/users/logout
Authorization: FixGuru eyJhbGciOiJIUzI1NiJ9...
Content-Type: application/x-www-form-urlencoded

refreshToken=eyJhbGciOiJIUzI1NiJ9...
```

## 🔐 认证说明

### JWT认证机制
- **Token类型**: FixGuru Bearer Token
- **请求头格式**: `Authorization: FixGuru <access_token>`
- **Access Token有效期**: 24小时
- **Refresh Token有效期**: 30天

### 接口分类

#### 🌐 公开接口（无需认证）
- `POST /v1/users/register` - 用户注册
- `POST /v1/users/login` - 用户登录
- `POST /v1/users/forgot-password` - 忘记密码
- `POST /v1/users/refresh-token` - 刷新token
- `GET /v1/users/check/email/{email}` - 检查邮箱是否存在
- `GET /v1/health` - 基础健康检查
- `GET /v1/health/detailed` - 详细健康检查

#### 🔒 认证接口（需要Access Token）
- `POST /v1/users/logout` - 用户登出
- `GET /v1/users/{userId}` - 获取用户信息（只能查看自己）
- `GET /v1/users/{userId}/status` - 检查用户状态（只能查看自己）
- `GET /v1/users/username/{username}` - 根据用户名获取用户信息
- `GET /v1/users/email/{email}` - 根据邮箱获取用户信息

## 📋 API分组说明

### 用户认证
包含用户注册、登录、登出和token管理相关接口。这些接口主要用于用户身份验证和会话管理。

### 用户管理
包含用户信息查询和状态管理接口。这些接口需要认证，用于管理用户数据。

### 系统监控
包含健康检查和系统状态监控接口。这些接口用于系统运维和监控。

## 🛠️ 在Knife4j中测试API

### 1. 设置认证
1. 点击页面右上角的"Authorize"按钮
2. 在"FixGuru-Auth"输入框中输入：`FixGuru <your_access_token>`
3. 点击"Authorize"确认

### 2. 测试流程
1. 先调用注册或登录接口获取access token
2. 复制返回的access token
3. 在认证框中输入：`FixGuru <access_token>`
4. 现在可以测试需要认证的接口了

### 3. 常见错误码
- `4001` - Access token is required
- `4002` - Invalid or expired access token
- `4003` - Access denied: You can only access your own resources

## 🌍 环境配置

### 开发环境
- **服务器**: http://localhost:8080/api
- **特点**: 详细日志、完整错误信息

### AWS Lambda环境
- **服务器**: https://yjy4cjhi2e.execute-api.us-west-1.amazonaws.com/api
- **特点**: 生产级性能、优化的冷启动

## 📝 注意事项

1. **Token安全**: 请妥善保管access token，不要在客户端明文存储
2. **权限控制**: 用户只能访问自己的资源，系统会自动验证权限
3. **Token过期**: access token过期后请使用refresh token获取新的token
4. **API限制**: 某些接口（如根据用户名/邮箱查询）仅用于系统内部功能

## 🔧 开发建议

1. **错误处理**: 始终检查API响应的code字段
2. **Token管理**: 实现自动token刷新机制
3. **权限验证**: 在客户端也要做相应的权限控制
4. **日志记录**: 记录重要的API调用用于调试

## 📞 技术支持

如有问题，请联系：
- **邮箱**: <EMAIL>
- **文档**: https://fixguru.com/docs
- **GitHub**: https://github.com/fixguru/fixguru-service
