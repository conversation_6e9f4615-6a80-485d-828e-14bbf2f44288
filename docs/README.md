# 📚 FixGuru 文档中心

欢迎来到FixGuru的文档中心！这里包含了项目的所有技术文档和指南。

## 📋 文档目录

### 🏗️ 架构设计
- **[初始架构设计参考文档](./初始架构设计文档.md)** - 项目初期的架构设计参考（实际实现有所简化）

### 🚀 部署与开发
- **[AWS部署与开发指南](./AWS部署与开发指南.md)** - AWS Lambda部署、环境配置、开发工具和脚本使用指南
- **[CICD部署指南](./CICD部署指南.md)** - CodePipeline自动化部署配置

## 🎯 快速导航

### 新手入门
1. 先阅读 **[AWS部署与开发指南](./AWS部署与开发指南.md)** 了解系统架构和配置
2. 按照指南配置环境和启动项目

### 本地开发
1. 加载开发环境变量：`source .env.dev`
2. 在IDEA中启动或使用：`mvn spring-boot:run`
3. 访问API文档：`http://localhost:8080/api/doc.html`

### 部署上线
1. 配置AWS凭证和环境变量
2. 选择环境：`source .env.dev` 或 `.env.test` 或 `.env.prod`
3. 一键部署：`./scripts/deploy.sh dev` (或test/prod)
4. 可选：设置 **[CICD部署指南](./CICD部署指南.md)** 实现自动化部署

## 📊 文档统计

| 文档类型 | 数量 | 说明 |
|---------|------|------|
| 架构设计 | 1 | 系统架构和设计理念 |
| 部署开发 | 2 | 部署指南、环境配置、开发工具 |
| **总计** | **3** | **精简高效的文档体系** |

## 🔄 文档维护

### 更新原则
- 所有文档都应保持最新状态
- 重大功能变更时及时更新相关文档
- 定期检查文档的准确性和完整性

### 文档特点
- **中文命名**: 所有文档都使用中文命名，便于理解
- **内容整合**: 相关内容已合并，避免重复
- **实用导向**: 专注于实际开发和部署需求

## 📞 联系方式

如果您在使用过程中遇到问题或有改进建议，请：
- 查看相关文档寻找解决方案
- 检查项目的issue列表
- 联系项目维护者

---

**文档版本**: v2.0  
**最后更新**: 2025-07-28  
**维护者**: Han

> 💡 **提示**: 建议按照文档的推荐顺序阅读，这样可以更好地理解项目的整体架构和使用方法。
